import { StrictMode } from "react";
import { createRoot } from "react-dom/client";
import "./index.css";
import App from "./App.jsx";
import { AuthProvider } from "./contexts/AuthContext";
import { ToastProvider } from "./contexts/ToastContext";
import PostsProvider from "./contexts/PostsContext";
import  CommentsProvider  from "./contexts/CommentsContext";
import { BrowserRouter } from "react-router-dom";

createRoot(document.getElementById("root")).render(
  <StrictMode>
    <BrowserRouter>
      <AuthProvider>
        <PostsProvider>
          <CommentsProvider>
          <ToastProvider>
            <App />
          </ToastProvider>
          </CommentsProvider>
        </PostsProvider>
      </AuthProvider>
    </BrowserRouter>
  </StrictMode>
);
