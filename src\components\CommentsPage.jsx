import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  But<PERSON>,
} from "@material-tailwind/react";
import defaultAvatar from "../assets/avatar.png";
import { useContext, useEffect, useState } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { AuthContext } from "../contexts/AuthContext";
import { CommentsContext } from "../contexts/CommentsContext";
import axios from "axios";

export default function CommentsPage() {
  const { postId } = useParams();
  const navigate = useNavigate();
  const { baseUrl } = useContext(AuthContext);
  const { addComment } = useContext(CommentsContext);
  const [post, setPost] = useState(null);
  const [loading, setLoading] = useState(true);
  const token = localStorage.getItem("token");

  const [commentBody, setCommentBody] = useState("");
  const [sending, setSending] = useState(false);

  useEffect(() => {
    if (!token) {
      setLoading(false);
      setPost(null);
      return;
    }

    axios
      .get(`${baseUrl}/posts/${postId}`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      })
      .then((response) => {
        setPost(response.data.data);
        setLoading(false);
      })
      .catch((error) => {
        console.error(error);
        setLoading(false);
      });
  }, [baseUrl, postId, token]);

  const handleAddComment = () => {
    if (!commentBody.trim()) return;
    setSending(true);

    axios
      .post(
        `${baseUrl}/posts/${post.id}/comments`,
        { body: commentBody },
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      )
      .then((response) => {
        // تحديث التعليقات في الواجهة فورًا
        setPost((prevPost) => ({
          ...prevPost,
          comments: [...(prevPost.comments || []), response.data.data],
          comments_count: (prevPost.comments_count || 0) + 1,
        }));
        // تحديث التعليقات في الـ context العام
        addComment(response.data.data);
        setCommentBody("");
      })
      .catch((error) => {
        console.error(error);
        alert("Failed to add comment.");
      })
      .finally(() => {
        setSending(false);
      });
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <Typography
          variant="h6"
          color="blue-gray"
          className="text-center bg-blue-50 p-6 rounded-md shadow-md"
        >
          Loading...
        </Typography>
      </div>
    );
  }

  if (!token) {
    return (
      <div className="flex flex-col justify-center items-center min-h-screen gap-4 px-4">
        <Typography
          variant="h5"
          color="red"
          className="text-center bg-red-100 p-6 rounded-md shadow-md max-w-md"
        >
          🚫 Please Sign In First To View Comments
        </Typography>
      </div>
    );
  }

  if (!post) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <Typography
          variant="h6"
          color="gray"
          className="text-center bg-gray-100 p-6 rounded-md shadow-md"
        >
          Post not found
        </Typography>
      </div>
    );
  }

  return (
    <div className="max-w-2xl mx-auto my-5">
      <button
        onClick={() => navigate(-1)}
        className="bg-blue-500 text-white px-3 py-2 rounded-lg hover:bg-blue-600 transition-all duration-300 ease-in-out"
      >
        Back
      </button>

      <Card className="shadow-lg">
        {/* معلومات الكاتب */}
        <div className="flex items-center gap-2 p-2">
          <img
            src={post.author?.profile_image || defaultAvatar}
            onError={(e) => {
              e.currentTarget.src = defaultAvatar;
            }}
            alt={post.author?.username || "User"}
            className="h-8 w-8 rounded-full object-cover"
          />
          <Typography variant="small" color="gray" className="font-semibold">
            {post.author?.username || "Unknown"}
          </Typography>
        </div>

        {/* صورة البوست */}
        <CardHeader className="h-72">
          <img
            src={post.image}
            alt="Post cover"
            className="h-full w-full object-cover rounded-lg"
          />
        </CardHeader>

        {/* نص البوست */}
        <CardBody>
          <Typography
            variant="h4"
            className="mb-2 text-2xl first-letter:capitalize first-letter:text-red-700 underline font-semibold "
          >
            {post.title}
          </Typography>
          <Typography variant="paragraph" color="blue-gray" className="my-1">
            {post.body}
          </Typography>
          <hr className="border border-gray-400" />
        </CardBody>

        {/* قسم التعليقات */}
        <CardBody>
          <Typography variant="h6" className="mb-4">
            Comments ({post.comments_count || 0})
          </Typography>
          {!post.comments || post.comments.length === 0 ? (
            <Typography color="gray">No comments yet.</Typography>
          ) : (
            post.comments.map((comment) => (
              <div
                key={comment.id}
                className="mb-4 p-3 border rounded-lg bg-gray-50 shadow-sm"
              >
                <div className="flex items-center gap-3 mb-2">
                  <img
                    src={comment.author?.profile_image || defaultAvatar}
                    onError={(e) => {
                      e.currentTarget.src = defaultAvatar;
                    }}
                    alt={comment.author?.username || "User"}
                    className="h-8 w-8 rounded-full object-cover"
                  />
                  <div>
                    <Typography className="font-semibold text-sm">
                      {comment.author?.username || "Unknown"}
                    </Typography>
                  </div>
                </div>
                <Typography>{comment.body}</Typography>
              </div>
            ))
          )}
        </CardBody>

        {/* تاغات */}
        <CardFooter>
          <div className="flex items-center gap-2 flex-wrap ">
            {post.tags && post.tags.length > 0 && (
              <div className="flex gap-2">
                {post.tags.map((tag) => (
                  <Chip
                    key={tag.id}
                    variant="ghost"
                    size="sm"
                    value={tag.name}
                    className="text-gray-300 bg-gray-800 p-1"
                  >
                    <span>{tag.name}</span>
                  </Chip>
                ))}
              </div>
            )}
          </div>

          {token ? (
            <div className="flex items-center gap-2 mt-4 justify-center">
              <input
                type="text"
                placeholder="Add a comment..."
                className="h-10 w-full border rounded px-3 py-2 outline-none focus:ring-2 focus:ring-blue-500"
                value={commentBody}
                onChange={(e) => setCommentBody(e.target.value)}
                disabled={sending}
                onKeyDown={(e) => {
                  if (e.key === "Enter") {
                    handleAddComment();
                  }
                }}
              />
              <Button
                size="sm"
                className="bg-blue-600 text-white hover:bg-blue-700 h-10"
                onClick={handleAddComment}
                disabled={sending}
              >
                {sending ? "Sending..." : "Add"}
              </Button>
            </div>
          ) : (
            <Typography color="gray" className="text-center">
              Please log in to add a comment.
            </Typography>
          )}
        </CardFooter>
      </Card>
    </div>
  );
}
