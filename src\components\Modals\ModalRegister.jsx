import React from "react";
import { useContext } from "react";
import axios from "axios";
import { AuthContext } from "../../contexts/AuthContext";
import { ToastContext } from "../../contexts/ToastContext";

export default function ModalRegister({ open, onClose }) {
  const { showToast } = useContext(ToastContext);
  const { baseUrl } = useContext(AuthContext);
  const [username, setUsername] = React.useState("");
  const [name, setName] = React.useState("");
  const [profileImage, setProfileImage] = React.useState(null);
  const [password, setPassword] = React.useState("");

  if (!open) return null;

  function handleRegisterRequest(e) {
    e.preventDefault();
    const formData = new FormData();
    formData.append("username", username);
    formData.append("name", name);
    formData.append("password", password);
    formData.append("image", profileImage);

    axios
      .post(`${baseUrl}/register`, formData /*, {
        headers: { "Content-Type": "multipart/form-data" } 
      }*/)
      .then((response) => {
        console.log(response.data);
        localStorage.setItem("token", response.data.token);
        localStorage.setItem("User", JSON.stringify(response.data.user));
        showToast("Registration completed successfully.", "success");
        setTimeout(() => {
          window.location.reload();
        }, 4000);
        onClose();
      })
      .catch((error) => {
        showToast(
          error.response?.data?.message || "Something went wrong",
          "error"
        );
      });
  }

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
      <div className="bg-white rounded-lg shadow-lg w-full max-w-sm p-6 relative">
        {/* Close Button */}
        <button
          onClick={onClose}
          className="absolute top-3 right-3 text-gray-500 hover:text-gray-800"
        >
          ✕
        </button>

        {/* Title */}
        <h2 className="text-xl font-semibold mb-1">Create Account</h2>
        <p className="text-gray-600 mb-6">
          Join us by creating a new account
        </p>

        {/* Register Form */}
        <form className="space-y-4" onSubmit={handleRegisterRequest}>
          {/* Username */}
          <div>
            <label
              htmlFor="username"
              className="block text-sm font-medium mb-1"
            >
              Username
            </label>
            <input
              id="username"
              type="text"
              placeholder="Your Username"
              className="w-full border rounded px-3 py-2 outline-none focus:ring-2 focus:ring-blue-500"
              value={username}
              onChange={(e) => setUsername(e.target.value)}
            />
          </div>

          {/* Name */}
          <div>
            <label htmlFor="name" className="block text-sm font-medium mb-1">
              Name
            </label>
            <input
              id="name"
              type="text"
              placeholder="Your Name"
              className="w-full border rounded px-3 py-2 outline-none focus:ring-2 focus:ring-blue-500"
              value={name}
              onChange={(e) => setName(e.target.value)}
            />
          </div>

          {/* Profile Image */}
          <div>
            <label
              htmlFor="profileImage"
              className="block text-sm font-medium mb-1"
            >
              Profile Image
            </label>
            <input
              id="profileImage"
              type="file"
              accept="image/*"
              className="w-full border rounded px-3 py-2 outline-none focus:ring-2 focus:ring-blue-500"
              onChange={(e) => setProfileImage(e.target.files[0])}
            />
          </div>

          {/* Password */}
          <div>
            <label
              htmlFor="password"
              className="block text-sm font-medium mb-1"
            >
              Password
            </label>
            <input
              id="password"
              type="password"
              placeholder="************"
              className="w-full border rounded px-3 py-2 outline-none focus:ring-2 focus:ring-blue-500"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
            />
          </div>

          {/* Submit Button */}
          <button
            type="submit"
            className="w-full bg-blue-600 text-white py-2 rounded hover:bg-blue-700"
          >
            Register
          </button>
        </form>
      </div>
    </div>
  );
}

