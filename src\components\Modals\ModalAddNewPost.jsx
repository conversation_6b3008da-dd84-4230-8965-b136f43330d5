import React, { useContext, useEffect, useState } from "react";
import axios from "axios";
import { ToastContext } from "../../contexts/ToastContext";
import { AuthContext } from "../../contexts/AuthContext";

export default function ModalAddNewPost({ open, onClose, postData }) {
  const { showToast } = useContext(ToastContext);
  const { baseUrl } = useContext(AuthContext);

  const [title, setTitle] = useState("");
  const [body, setBody] = useState("");
  const [image, setImage] = useState(null);
  const [sending, setSending] = useState(false);

  useEffect(() => {
    if (postData) {
      setTitle(postData.title || "");
      setBody(postData.body || "");
      setImage(null);
    } else {
      setTitle("");
      setBody("");
      setImage(null);
    }
  }, [postData, open]);

  if (!open) return null;

  const handleSubmit = async (e) => {
    e.preventDefault();
    setSending(true);

    try {
      const token = localStorage.getItem("token");

      if (postData) {
        // تعديل البوست - استخدام POST مع _method
        const formData = new FormData();
        formData.append("_method", "PUT");
        formData.append("title", title);
        formData.append("body", body);
        if (image) formData.append("image", image);

        const response = await axios.post(
          `${baseUrl}/posts/${postData.id}`,
          formData,
          {
            headers: {
              "Content-Type": "multipart/form-data",
              Authorization: `Bearer ${token}`,
            },
          }
        );

        console.log("Update response:", response.data);
        showToast("Post updated successfully!", "success");
      } else {
        // إضافة بوست جديد
        const formData = new FormData();
        formData.append("title", title);
        formData.append("body", body);
        if (image) formData.append("image", image);

        const response = await axios.post(`${baseUrl}/posts`, formData, {
          headers: {
            "Content-Type": "multipart/form-data",
            Authorization: `Bearer ${token}`,
          },
        });

        console.log("Create response:", response.data);
        showToast("Post added successfully!", "success");
      }

      // إعادة تحميل الصفحة لإظهار التغييرات
      setTimeout(() => {
        window.location.reload();
      }, 1500);

      onClose();
    } catch (error) {
      console.error("Error:", error);
      showToast(
        error.response?.data?.message || "Something went wrong!",
        "error"
      );
    } finally {
      setSending(false);
    }
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
      <div className="bg-white rounded-lg shadow-lg w-full max-w-md p-6 relative">
        <button
          onClick={onClose}
          className="absolute top-3 right-3 text-gray-500 hover:text-gray-800"
          disabled={sending}
        >
          ✕
        </button>

        <h2 className="text-xl font-semibold mb-1">
          {postData ? "Edit Post" : "Add New Post"}
        </h2>
        <p className="text-gray-600 mb-6">
          {postData
            ? "Update the form below to edit the post"
            : "Fill the form below to add a post"}
        </p>

        <form className="space-y-4" onSubmit={handleSubmit}>
          <div>
            <label htmlFor="title" className="block text-sm font-medium mb-1">
              Title
            </label>
            <input
              id="title"
              type="text"
              placeholder="Enter post title"
              className="w-full border rounded px-3 py-2 outline-none focus:ring-2 focus:ring-blue-500"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              required
              disabled={sending}
            />
          </div>

          <div>
            <label htmlFor="body" className="block text-sm font-medium mb-1">
              Body
            </label>
            <textarea
              id="body"
              rows="4"
              placeholder="Write your post content here..."
              className="w-full border rounded px-3 py-2 outline-none focus:ring-2 focus:ring-blue-500 resize-none"
              value={body}
              onChange={(e) => setBody(e.target.value)}
              required
              disabled={sending}
            />
          </div>

          <div>
            <label htmlFor="image" className="block text-sm font-medium mb-1">
              Image
            </label>
            <input
              id="image"
              type="file"
              accept="image/*"
              className="w-full border rounded px-3 py-2 outline-none focus:ring-2 focus:ring-blue-500"
              onChange={(e) => setImage(e.target.files[0])}
              disabled={sending}
            />
          </div>

          <button
            type="submit"
            className="w-full bg-blue-600 text-white py-2 rounded hover:bg-blue-700 disabled:opacity-70"
            disabled={sending}
          >
            {sending
              ? postData
                ? "Saving..."
                : "Adding..."
              : postData
              ? "Save Changes"
              : "Add Post"}
          </button>
        </form>
      </div>
    </div>
  );
}
