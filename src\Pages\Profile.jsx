import React from "react";
import DefaultImageProfile from "../assets/avatar.png";
import { useContext } from "react";
import { AuthContext } from "../contexts/AuthContext";
import { PostsContext } from "../contexts/PostsContext";
import { CommentsContext } from "../contexts/CommentsContext";

function Profile() {
  const { posts } = useContext(PostsContext);
  const { comments } = useContext(CommentsContext);
  const { user } = useContext(AuthContext);

  if (!user) {
    return <div>Loading...</div>;
  }

  // فلترة المنشورات للمستخدم الحالي فقط
  const userPosts = posts.filter((post) => post.author?.id === user.id);

  // حساب عدد التعليقات التي كتبها المستخدم (من الـ context)
  const userCommentsFromContext = comments.filter(
    (comment) => comment.author?.id === user.id
  );

  // إجمالي التعليقات = فقط التعليقات التي كتبها المستخدم
  const totalUserComments = userCommentsFromContext.length;

  return (
    <div className="min-h-screen bg-gray-100 flex justify-center items-center p-4">
      <div className="bg-white max-w-2xl w-full rounded-2xl shadow-lg overflow-hidden">
        <div className="bg-gradient-to-r from-red-500 to-gray-700 h-40 relative">
          <img
            src={DefaultImageProfile}
            alt="Profile"
            className="w-20 h-20 rounded-full border-4 border-white shadow-lg object-cover absolute -bottom-16 left-1/2 transform -translate-x-1/2"
          />
        </div>
        <div className="mt-20 text-center px-6 pb-6">
          <h1 className="text-2xl font-bold text-gray-800">{user.username}</h1>
          <p className="text-gray-500">Username: {user.username}</p>
          <div className="mt-6 grid grid-cols-2 gap-4">
            <div className="bg-gray-50 p-4 rounded-lg shadow-sm">
              <h2 className="text-lg font-semibold text-gray-700">Posts</h2>
              <p className="text-2xl font-bold text-red-600">
                {userPosts.length}
              </p>
            </div>
            <div className="bg-gray-50 p-4 rounded-lg shadow-sm">
              <h2 className="text-lg font-semibold text-gray-700">Comments</h2>
              <p className="text-2xl font-bold text-red-600">
                {totalUserComments}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default Profile;
