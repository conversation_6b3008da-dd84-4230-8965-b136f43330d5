import React from "react";
import { Routes, Route } from "react-router-dom";
import NavList from "./components/NavList";
import CardPost from "./components/CardPost";
import Toast from "./components/Toast";
import AddNewPost from "./components/AddNewPost";
import CommentsPage from "./components/CommentsPage";
import Profile from "./Pages/Profile";

function App() {
  return (
    <div className="App bg-gray-100 container mx-auto">
      <NavList />
      <Routes>
        <Route
          path="/"
          element={
            <>
              <CardPost />
              <AddNewPost />
            </>
          }
        />
        <Route path="/comments/:postId" element={<CommentsPage />} />
        <Route path="/profile" element={<Profile />} />
        {/* <Route path="*" element={<div>Page not found</div>} /> */}

      </Routes>

      <Toast />
    </div>
  );
}

export default App;
