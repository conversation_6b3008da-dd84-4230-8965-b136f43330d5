import { createContext, useEffect, useState } from "react";

export const CommentsContext = createContext();

export default function CommentsProvider({ children }) {
  const [comments, setComments] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchComments();
  }, []);

  const fetchComments = async () => {
    try {
      // جلب جميع المنشورات للحصول على التعليقات
      const response = await fetch("https://tarmeezacademy.com/api/v1/posts");
      const data = await response.json();

      // استخراج جميع التعليقات من جميع المنشورات
      const allComments = [];
      data.data.forEach((post) => {
        if (post.comments && post.comments.length > 0) {
          allComments.push(...post.comments);
        }
      });

      setComments(allComments);
      setLoading(false);
    } catch (error) {
      console.error("Failed to fetch comments", error);
      setLoading(false);
    }
  };

  const addComment = (newComment) => {
    setComments((prevComments) => [newComment, ...prevComments]);
  };

  const updateComment = (updatedComment) => {
    setComments((prevComments) =>
      prevComments.map((comment) =>
        comment.id === updatedComment.id ? updatedComment : comment
      )
    );
  };

  const deleteComment = (commentId) => {
    setComments((prevComments) =>
      prevComments.filter((comment) => comment.id !== commentId)
    );
  };

  return (
    <CommentsContext.Provider
      value={{
        comments,
        loading,
        fetchComments,
        addComment,
        updateComment,
        deleteComment,
      }}
    >
      {children}
    </CommentsContext.Provider>
  );
}
