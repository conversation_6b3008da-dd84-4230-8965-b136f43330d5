
import React, { createContext, useEffect, useState } from "react";

export const CommentsContext = createContext();

export default function CommentsProvider({ children }) {
  const [comments, setComments] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchComments();
  }, []);

  const fetchComments = async () => {
    try {
      const response = await fetch("https://tarmeezacademy.com/api/v1/posts");
      const data = await response.json();
      setComments(data.data);
      setLoading(false);
    } catch (error) {
      console.error("Failed to fetch comments", error);
      setLoading(false);
    }
  };

  return (
    <CommentsContext.Provider value={{ comments, loading, fetchComments }}>
      {children}
    </CommentsContext.Provider>
  );
}
