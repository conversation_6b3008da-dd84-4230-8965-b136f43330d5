import { createContext, useState } from "react";

export const CommentsContext = createContext();

export default function CommentsProvider({ children }) {
  const [comments, setComments] = useState([]);
  const [loading, setLoading] = useState(false);

  const fetchComments = async () => {
    try {
      // بدلاً من جلب جميع التعليقات، سنبدأ بقائمة فارغة
      // ونعتمد على إضافة التعليقات عند حدوثها
      setComments([]);
      setLoading(false);
    } catch (error) {
      console.error("Failed to fetch comments", error);
      setLoading(false);
    }
  };

  const addComment = (newComment) => {
    setComments((prevComments) => [newComment, ...prevComments]);
  };

  const updateComment = (updatedComment) => {
    setComments((prevComments) =>
      prevComments.map((comment) =>
        comment.id === updatedComment.id ? updatedComment : comment
      )
    );
  };

  const deleteComment = (commentId) => {
    setComments((prevComments) =>
      prevComments.filter((comment) => comment.id !== commentId)
    );
  };

  return (
    <CommentsContext.Provider
      value={{
        comments,
        loading,
        fetchComments,
        addComment,
        updateComment,
        deleteComment,
      }}
    >
      {children}
    </CommentsContext.Provider>
  );
}
