import { createContext, useState, useEffect } from "react";

export const CommentsContext = createContext();

export default function CommentsProvider({ children }) {
  const [comments, setComments] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchComments();
  }, []);

  const fetchComments = async () => {
    try {
      // جلب بعض المنشورات الحديثة للحصول على التعليقات
      const response = await fetch("https://tarmeezacademy.com/api/v1/posts");
      const data = await response.json();

      // جلب تفاصيل أول 5 منشورات للحصول على التعليقات
      const allComments = [];
      const firstFivePosts = data.data.slice(0, 5);

      for (const post of firstFivePosts) {
        if (post.comments_count > 0) {
          try {
            const postResponse = await fetch(
              `https://tarmeezacademy.com/api/v1/posts/${post.id}`
            );
            const postData = await postResponse.json();

            if (postData.data.comments && postData.data.comments.length > 0) {
              allComments.push(...postData.data.comments);
            }
          } catch (error) {
            console.error(
              `Failed to fetch comments for post ${post.id}`,
              error
            );
          }
        }
      }

      setComments(allComments);
      setLoading(false);
    } catch (error) {
      console.error("Failed to fetch comments", error);
      setLoading(false);
    }
  };

  const addComment = (newComment) => {
    setComments((prevComments) => [newComment, ...prevComments]);
  };

  const updateComment = (updatedComment) => {
    setComments((prevComments) =>
      prevComments.map((comment) =>
        comment.id === updatedComment.id ? updatedComment : comment
      )
    );
  };

  const deleteComment = (commentId) => {
    setComments((prevComments) =>
      prevComments.filter((comment) => comment.id !== commentId)
    );
  };

  return (
    <CommentsContext.Provider
      value={{
        comments,
        loading,
        fetchComments,
        addComment,
        updateComment,
        deleteComment,
      }}
    >
      {children}
    </CommentsContext.Provider>
  );
}
