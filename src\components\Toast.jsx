import React, { useContext, useEffect, useState } from "react";
import { ToastContext } from "../contexts/ToastContext";
import { MdCheckCircle, MdClose, MdWarning, MdInfo } from "react-icons/md";

export default function Toast() {
  const { toast } = useContext(ToastContext);
  const [visible, setVisible] = useState(false);

  useEffect(() => {
    if (toast) {
      setVisible(true);
      const timer = setTimeout(() => {
        setVisible(false);
      }, 4000);
      return () => clearTimeout(timer);
    }
  }, [toast]);

  if (!toast || !visible) return null;

  const variants = {
    success: {
      color: "bg-green-50 text-green-800 border-green-200",
      icon: MdCheckCircle,
      iconColor: "text-green-500",
    },
    error: {
      color: "bg-red-50 text-red-800 border-red-200",
      icon: MdClose,
      iconColor: "text-red-500",
    },
    warning: {
      color: "bg-yellow-50 text-yellow-800 border-yellow-200",
      icon: MdWarning,
      iconColor: "text-yellow-500",
    },
    info: {
      color: "bg-blue-50 text-blue-800 border-blue-200",
      icon: MdInfo,
      iconColor: "text-blue-500",
    },
  };

  const variant = variants[toast.type] || variants.info;
  const IconComponent = variant.icon;

  return (
    <div
      className={`fixed bottom-6 right-6 z-50 min-w-80 max-w-md border-l-4 px-5 py-4 rounded-lg shadow-xl backdrop-blur-sm ${variant.color} transition-all duration-300 transform hover:scale-105`}
      role="alert"
    >
      <div className="flex items-start space-x-3">
        <div className="flex-shrink-0">
          <IconComponent className={`w-6 h-6 ${variant.iconColor}`} />
        </div>
        <div className="flex-1">
          <p className="font-semibold text-base leading-relaxed">{toast.message}</p>
        </div>
      </div>
    </div>
  );
}
