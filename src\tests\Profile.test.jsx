import { render, screen } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import Profile from '../Pages/Profile';
import { AuthContext } from '../contexts/AuthContext';
import { PostsContext } from '../contexts/PostsContext';
import { CommentsContext } from '../contexts/CommentsContext';

// Mock data
const mockUser = {
  id: 1,
  username: 'testuser'
};

const mockPosts = [
  { id: 1, title: 'Post 1', author: { id: 1 } },
  { id: 2, title: 'Post 2', author: { id: 2 } },
  { id: 3, title: 'Post 3', author: { id: 1 } }
];

const mockComments = [
  { id: 1, body: 'Comment 1', author: { id: 1 } },
  { id: 2, body: 'Comment 2', author: { id: 2 } },
  { id: 3, body: 'Comment 3', author: { id: 1 } },
  { id: 4, body: 'Comment 4', author: { id: 1 } }
];

const renderWithProviders = (user = mockUser, posts = mockPosts, comments = mockComments) => {
  return render(
    <AuthContext.Provider value={{ user, loading: false }}>
      <PostsContext.Provider value={{ posts, loading: false }}>
        <CommentsContext.Provider value={{ comments, loading: false }}>
          <Profile />
        </CommentsContext.Provider>
      </PostsContext.Provider>
    </AuthContext.Provider>
  );
};

describe('Profile Component', () => {
  it('should display correct user posts count', () => {
    renderWithProviders();
    
    // Should show 2 posts for user with id 1
    expect(screen.getByText('2')).toBeInTheDocument();
  });

  it('should display correct user comments count', () => {
    renderWithProviders();
    
    // Should show 3 comments for user with id 1
    expect(screen.getByText('3')).toBeInTheDocument();
  });

  it('should display username', () => {
    renderWithProviders();
    
    expect(screen.getByText('testuser')).toBeInTheDocument();
  });

  it('should show loading when user is null', () => {
    renderWithProviders(null);
    
    expect(screen.getByText('Loading...')).toBeInTheDocument();
  });

  it('should filter posts and comments correctly for different user', () => {
    const differentUser = { id: 2, username: 'user2' };
    renderWithProviders(differentUser);
    
    // User 2 should have 1 post and 1 comment
    const counts = screen.getAllByText('1');
    expect(counts).toHaveLength(2); // One for posts, one for comments
  });
});
