import * as React from "react";
import {
  <PERSON><PERSON>,
  <PERSON>con<PERSON>utt<PERSON>,
  <PERSON>po<PERSON>,
  <PERSON>lapse,
  Navbar,
} from "@material-tailwind/react";
import DefaultImageProfile from "../assets/avatar.png";
import { Home, Menu, ProfileCircle, Xmark } from "iconoir-react";
import ModalLogin from "./Modals/ModalLogin.jsx";
import ModalRegister from "./Modals/ModalRegister.jsx";
import { useContext } from "react";
import { AuthContext } from "../contexts/AuthContext.jsx";
import { ToastContext } from "../contexts/ToastContext";

const LINKS = [
  {
    icon: Home,
    title: "Home",
    href: "/",
  },
  {
    icon: ProfileCircle,
    title: "Profile",
    href: "/profile",
  },
];

function NavList() {
  return (
    <ul className="mt-4 flex flex-col gap-x-3 gap-y-1.5 lg:mt-0 lg:flex-row lg:items-center">
      {LINKS.map(({ icon: Icon, title, href }) => (
        <li key={title}>
          <Typography
            as="a"
            href={href}
            type="small"
            className="flex items-center gap-x-2 p-1 text-white hover:text-white"
          >
            <Icon className="h-4 w-4" />
            {title}
          </Typography>
        </li>
      ))}
    </ul>
  );
}

export default function DarkNavbar() {
  const [openNav, setOpenNav] = React.useState(false);
  const [openModal, setOpenModal] = React.useState(false);
  const [type, setType] = React.useState("login");
  const { user } = useContext(AuthContext);
  const { showToast } = useContext(ToastContext);

  const handleOpenModal = (type) => {
    setType(type);
    setOpenModal(true);
  };
  const handleCloseModal = () => {
    setOpenModal(false);
  };

  React.useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth >= 960) setOpenNav(false);
    };
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  return (
    <>
      <Navbar className="mx-auto max-w-2xl px-4 bg-black dark:bg-surface-dark">
        <div className="flex flex-wrap items-center justify-between text-white py-2">
          <Typography
            as="a"
            href="#"
            type="h3"
            className="ml-2 mr-2 block py-1 font-bold"
          >
            V <span className="text-red-500">S</span>
          </Typography>

          <hr className="ml-1 mr-1.5 hidden h-5 w-px border-l border-t-0 border-surface/25 lg:block dark:border-surface" />

          <div className="hidden lg:block">
            <NavList />
          </div>

          <div className="hidden lg:flex lg:ml-auto items-center gap-4">
            {user ? (
              <>
                <div className="flex items-center gap-2">
                  <img
                    src={user.profile_image || DefaultImageProfile}
                    alt={user.username}
                  className="h-8 w-8 rounded-full object-cover border border-gray-300"
                    onError={(e) => {
                      e.currentTarget.onerror = null; // عشان ما يدخلش هنا تاني لو الصورة الافتراضية كمان ما اتحملت
                      e.currentTarget.src = DefaultImageProfile;
                    }}
                  />

                  <Typography
                    as="a"
                    href="#"
                    type="small"
                    className="flex items-center gap-x-2 p-1 text-white hover:text-white"
                  >
                    {user.username}
                  </Typography>
                </div>

                <Button
                  size="md"
                  className="bg-red-600 text-white hover:border-white hover:bg-white hover:text-red-600"
                  onClick={() => {
                    localStorage.removeItem("token");
                    localStorage.removeItem("User");
                    showToast(
                      "Logout completed successfully. Your session has ended securely.",
                      "success"
                    );

                    setTimeout(() => {
                      window.location.reload();
                    }, 4000);
                  }}
                >
                  Logout
                </Button>
              </>
            ) : (
              <>
                <Button
                  size="md"
                  className="border-white bg-white text-black hover:border-white hover:bg-white hover:text-black"
                  onClick={() => handleOpenModal("login")}
                >
                  Login
                </Button>
                <Button
                  size="md"
                  className="border-white bg-white text-black hover:border-white hover:bg-white hover:text-black"
                  onClick={() => handleOpenModal("register")}
                >
                  Register
                </Button>
              </>
            )}
          </div>

          <IconButton
            size="sm"
            color="secondary"
            onClick={() => setOpenNav(!openNav)}
            className="ml-auto grid lg:hidden"
          >
            {openNav ? (
              <Xmark className="h-4 w-4" />
            ) : (
              <Menu className="h-4 w-4" />
            )}
          </IconButton>
        </div>

        <Collapse open={openNav}>
          <NavList />
          {user ? (
            <div className="flex flex-col gap-3 p-4">
              <div className="flex items-center gap-2">
                <img
                  src={user.profile_image || DefaultImageProfile}
                  alt={user.username}
                  className="h-8 w-8 rounded-full object-cover border border-gray-300"
                  onError={(e) => {
                    e.currentTarget.onerror = null; // عشان ما يدخلش هنا تاني لو الصورة الافتراضية كمان ما اتحملت
                    e.currentTarget.src = DefaultImageProfile;
                  }}
                />

                <Typography
                  as="a"
                  href="#"
                  type="small"
                  className="flex items-center gap-x-2 p-1 text-white "
                >
                  {user.username}
                </Typography>
              </div>
              <Button
                size="sm"
                fullWidth
                className="bg-red-600 text-white hover:bg-red-700"
                onClick={() => {
                  localStorage.removeItem("token");
                  localStorage.removeItem("User");
                  showToast(
                    "Logout completed successfully. Your session has ended securely.",
                    "success"
                  );
                  setOpenNav(false);
                  setTimeout(() => {
                    window.location.reload();
                  }, 4000);
                }}
              >
                Logout
              </Button>
            </div>
          ) : (
            <>
              <Button
                size="sm"
                fullWidth
                className="mt-4 border border-black bg-white text-black hover:bg-gray-100"
                onClick={() => {
                  handleOpenModal("login");
                  setOpenNav(false);
                }}
              >
                Login
              </Button>
              <Button
                size="sm"
                fullWidth
                className="mt-2 border border-black bg-white text-black hover:bg-gray-100"
                onClick={() => {
                  handleOpenModal("register");
                  setOpenNav(false);
                }}
              >
                Register
              </Button>
            </>
          )}
        </Collapse>
      </Navbar>

      {type === "login" ? (
        <ModalLogin open={openModal} onClose={handleCloseModal} />
      ) : (
        <ModalRegister open={openModal} onClose={handleCloseModal} />
      )}
    </>
  );
}
