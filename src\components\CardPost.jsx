import {
  <PERSON>,
  <PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON>,
  CardFooter,
  <PERSON>po<PERSON>,
  <PERSON>,
} from "@material-tailwind/react";
import axios from "axios";
import defaultAvatar from "../assets/avatar.png";
import { useState, useContext } from "react";
import { IoSend } from "react-icons/io5";
import { AuthContext } from "../contexts/AuthContext";
import { ToastContext } from "../contexts/ToastContext";
import { useNavigate } from "react-router-dom";
import ModalAddNewPost from "./Modals/ModalAddNewPost";
import { PostsContext } from "../contexts/PostsContext";

export default function CardPost() {
  const navigate = useNavigate();
  const { baseUrl, user } = useContext(AuthContext);
  const { showToast } = useContext(ToastContext);
  const { posts } = useContext(PostsContext);

  const [openModal, setOpenModal] = useState(false);
  const [editingPost, setEditingPost] = useState(null);

  // تفتح المودال مع تعيين البوست المراد تعديله
  const handleEditClick = (post) => {
    setEditingPost(post);
    setOpenModal(true);
  };

  // حذف البوست
  const handleDeleteClick = (postId) => {
    const confirmDelete = window.confirm("هل أنت متأكد أنك تريد حذف البوست؟");

    if (!confirmDelete) return; // لو رفض، نخرج من الدالة

    axios
      .delete(`${baseUrl}/posts/${postId}`, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem("token")}`,
        },
      })
      .then(() => {
        showToast("Post deleted successfully!", "success");
        setTimeout(() => {
          window.location.reload();
        }, 1500);
      })
      .catch((error) => {
        showToast(
          error.response?.data?.message || "Something went wrong!",
          "error"
        );
      });
  };

  return (
    <>
      {posts.map((post) => (
        <Card key={post.id} className="max-w-2xl mx-auto my-5 shadow-lg">
          <div className="flex justify-between">
            <div className="flex items-center gap-2 p-2">
              <img
                src={post.author?.profile_image || defaultAvatar}
                onError={(e) => (e.currentTarget.src = defaultAvatar)}
                alt={post.author?.username || "User"}
                className="h-8 w-8 rounded-full object-cover"
              />
              <Typography
                variant="small"
                color="gray"
                className="font-semibold"
              >
                {post.author?.username}
              </Typography>
            </div>

            <div className="flex justify-end gap-2 p-2">
              {user?.id === post.author?.id && (
                <div className="editBtn">
                  <button
                    onClick={() => handleEditClick(post)}
                    className="bg-blue-500 text-white px-3 py-2 rounded-lg hover:bg-blue-600 transition-all duration-300 ease-in-out"
                  >
                    Edit
                  </button>
                </div>
              )}

              {user?.id === post.author?.id && (
                <div className="deleteBtn">
                  <button
                    onClick={() => handleDeleteClick(post.id)}
                    className="bg-red-500 text-white px-3 py-2 rounded-lg hover:bg-red-600 transition-all duration-300 ease-in-out"
                  >
                    Delete
                  </button>
                </div>
              )}
            </div>
          </div>

          <CardHeader className="h-72">
            <img
              src={post.image}
              alt="Post cover"
              className="h-full w-full object-cover rounded-lg"
            />
          </CardHeader>

          <CardBody>
            <Typography variant="small" color="gray">
              {post.created_at}
            </Typography>
            <Typography
              variant="h4"
              className="mb-2 text-2xl first-letter:capitalize first-letter:text-red-700 underline font-semibold "
            >
              {post.title}
            </Typography>
            <Typography variant="paragraph" color="blue-gray" className="my-1">
              {post.body}
            </Typography>
            <hr className="border border-gray-400" />
          </CardBody>

          <CardFooter>
            <div className="flex items-center gap-2 flex-wrap ">
              <Typography
                as="div"
                variant="small"
                color="gray"
                className="flex items-center gap-2 cursor-pointer hover:text-gray-900"
                onClick={() => navigate(`/comments/${post.id}`)}
              >
                <IoSend className="text-gray-500 hover:text-gray-900 transition-all duration-300" />
                ({post.comments_count}) Comments
              </Typography>
              {post.tags && post.tags.length > 0 ? (
                <div className="flex gap-2">
                  {post.tags.map((tag) => (
                    <Chip
                      key={tag.id}
                      variant="ghost"
                      size="sm"
                      value={tag.name}
                      className="text-gray-300 bg-gray-800 p-1"
                    >
                      <span>{tag.name}</span>
                    </Chip>
                  ))}
                </div>
              ) : null}
            </div>
          </CardFooter>
        </Card>
      ))}

      <ModalAddNewPost
        open={openModal}
        onClose={() => {
          setOpenModal(false);
          setEditingPost(null);
        }}
        postData={editingPost}
      />
    </>
  );
}
