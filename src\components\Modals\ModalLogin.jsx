import axios from "axios";
import React from "react";
import { useContext } from "react";
import { ToastContext } from "../../contexts/ToastContext";
import { AuthContext } from "../../contexts/AuthContext";

export default function Modal({ open, onClose }) {
  if (!open) return null;
  const { showToast } = useContext(ToastContext);
  const { setUser } = useContext(AuthContext);

  const [username, setUsername] = React.useState("omar00");
  const [password, setPassword] = React.useState("1234567");

  function LoginRequest(e) {
    e.preventDefault();
    axios
      .post("https://tarmeezacademy.com/api/v1/login", {
        username: username,
        password: password,
      })
      .then((response) => {
        localStorage.setItem("token", response.data.token);
        localStorage.setItem("User", JSON.stringify(response.data.user));
        setUser(response.data.user);
        showToast(
          "Login was successful. You can now access all your personalized features.",
          "success"
        );
        onClose();
      })
      .catch((error) => {
        console.log(error);
        showToast(error.response.data.message, "error");
      });
  }

  return (
    <>
      <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
        <div className="bg-white rounded-lg shadow-lg w-full max-w-sm p-6 relative">
          <button
            onClick={onClose}
            className="absolute top-3 right-3 text-gray-500 hover:text-gray-800"
          >
            ✕
          </button>

          <h2 className="text-xl font-semibold mb-1">Log In</h2>
          <p className="text-gray-600 mb-6">
            Enter your email and password to Log In.
          </p>

          <form className="space-y-4" onSubmit={LoginRequest}>
            <div>
              <label
                htmlFor="Username"
                className="block text-sm font-medium mb-1"
              >
                Username
              </label>
              <input
                id="Username"
                type="text"
                placeholder=" Please Enter Your Username "
                className="w-full border rounded px-3 py-2 outline-none focus:ring-2 focus:ring-blue-500"
                value={username}
                onChange={(e) => setUsername(e.target.value)}
              />
            </div>

            <div>
              <label
                htmlFor="password"
                className="block text-sm font-medium mb-1"
              >
                Password
              </label>
              <input
                id="password"
                type="password"
                placeholder="************"
                className="w-full border rounded px-3 py-2 outline-none focus:ring-2 focus:ring-blue-500"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
              />
            </div>

            <div className="flex items-center gap-2">
              <input id="checkbox" type="checkbox" className="h-4 w-4" />
              <label htmlFor="checkbox" className="text-sm text-gray-700">
                Remember Me
              </label>
            </div>

            <button
              type="submit"
              className="w-full bg-blue-600 text-white py-2 rounded hover:bg-blue-700"
            >
              Sign In
            </button>
          </form>
        </div>
      </div>
    </>
  );
}
