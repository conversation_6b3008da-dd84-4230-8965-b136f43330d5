import React, { useState } from "react";
import ModalAddNewPost from "./Modals/ModalAddNewPost";
import { useContext } from "react";
import { AuthContext } from "../contexts/AuthContext";

function AddNewPost() {
  const [openModal, setOpenModal] = useState(false);
  const { user } = useContext(AuthContext);
  if (!user) return null;

  return (
    <div>
      <div
        className="text-4xl bg-blue-500 text-white rounded-full w-14 h-14 flex items-center justify-center cursor-pointer hover:bg-blue-600 transition-all duration-300 ease-in-out"
        style={{ position: "fixed", bottom: "20px", right: "20px" }}
        onClick={() => setOpenModal(true)}
      >
        +
      </div>

      <ModalAddNewPost open={openModal} onClose={() => setOpenModal(false)} />
    </div>
  );
}

export default AddNewPost;
